import java.util.*;

// Enums for Vehicle and Parking Spot Types
enum VehicleType {
    BIKE, CAR, TRUCK
}

enum ParkingSpotType {
    SMALL, MEDIUM, LARGE
}

// ParkingLot as a Singleton
class ParkingLot {
    private static ParkingLot instance;
    private List<Level> levels;
    private ParkingSpotManager spotManager;
    private ParkingStrategy parkingStrategy;
    private PricingStrategy pricingStrategy;

    private ParkingLot(int numLevels, ParkingSpotManager spotManager, ParkingStrategy parkingStrategy, PricingStrategy pricingStrategy) {
        this.levels = new ArrayList<>();
        for (int i = 0; i < numLevels; i++) {
            this.levels.add(new Level(i));
        }
        this.spotManager = spotManager;
        this.parkingStrategy = parkingStrategy;
        this.pricingStrategy = pricingStrategy;
    }

    public static ParkingLot getInstance(int numLevels, ParkingSpotManager spotManager, ParkingStrategy parkingStrategy, PricingStrategy pricingStrategy) {
        if (instance == null) {
            instance = new ParkingLot(numLevels, spotManager, parkingStrategy, pricingStrategy);
        }
        return instance;
    }

    public boolean parkVehicle(Vehicle vehicle) {
        return parkingStrategy.park(vehicle, spotManager);
    }

    public double unparkVehicle(Ticket ticket) {
        ParkingSpot spot = spotManager.getSpot(ticket.getLevel(), ticket.getSpotId());
        long duration = (new Date().getTime() - ticket.getIssuedAt().getTime()) / (1000 * 60 * 60); // Duration in hours
        spotManager.releaseSpot(spot);
        return pricingStrategy.calculatePrice(duration, spot);
    }

    public void displayStatus() {
        spotManager.displayStatus();
    }
}

// Level Class
class Level {
    private int levelNumber;
    private List<ParkingSpot> spots;

    public Level(int levelNumber) {
        this.levelNumber = levelNumber;
        this.spots = new ArrayList<>();
    }

    public void addParkingSpots(List<ParkingSpot> newSpots) {
        spots.addAll(newSpots);
    }

    public ParkingSpot getSpot(int spotId) {
        return spots.get(spotId);
    }

    public void displayStatus() {
        System.out.println("Level " + levelNumber + " Status:");
        for (ParkingSpot spot : spots) {
            System.out.println("Spot ID: " + spot.getSpotId() + " - Occupied: " + (spot.isOccupied() ? "Yes" : "No"));
        }
    }
}

// ParkingSpot Class
class ParkingSpot {
    private int spotId;
    private ParkingSpotType spotType;
    private Vehicle vehicle;

    public ParkingSpot(int spotId, ParkingSpotType spotType) {
        this.spotId = spotId;
        this.spotType = spotType;
        this.vehicle = null;
    }

    public boolean canFitVehicle(Vehicle vehicle) {
        return this.vehicle == null && vehicle.getVehicleType().ordinal() <= this.spotType.ordinal();
    }

    public void parkVehicle(Vehicle vehicle) {
        this.vehicle = vehicle;
    }

    public void unparkVehicle() {
        this.vehicle = null;
    }

    public boolean isOccupied() {
        return vehicle != null;
    }

    public int getSpotId() {
        return spotId;
    }

    public ParkingSpotType getSpotType() {
        return spotType;
    }
}

// Abstract Vehicle Class
abstract class Vehicle {
    private String licensePlate;
    private VehicleType vehicleType;

    public Vehicle(String licensePlate, VehicleType vehicleType) {
        this.licensePlate = licensePlate;
        this.vehicleType = vehicleType;
    }

    public VehicleType getVehicleType() {
        return vehicleType;
    }

    public String getLicensePlate() {
        return licensePlate;
    }
}

// Specific Vehicle Classes
class Car extends Vehicle {
    public Car(String licensePlate) {
        super(licensePlate, VehicleType.CAR);
    }
}

class Bike extends Vehicle {
    public Bike(String licensePlate) {
        super(licensePlate, VehicleType.BIKE);
    }
}

class Truck extends Vehicle {
    public Truck(String licensePlate) {
        super(licensePlate, VehicleType.TRUCK);
    }
}

// Ticket Class
class Ticket {
    private int level;
    private int spotId;
    private Date issuedAt;

    public Ticket(int level, int spotId) {
        this.level = level;
        this.spotId = spotId;
        this.issuedAt = new Date();
    }

    public int getLevel() {
        return level;
    }

    public int getSpotId() {
        return spotId;
    }

    public Date getIssuedAt() {
        return issuedAt;
    }
}

// ParkingSpotManager Class
class ParkingSpotManager {
    Map<Integer, List<ParkingSpot>> levelToSpots;

    public ParkingSpotManager() {
        this.levelToSpots = new HashMap<>();
    }

    public void addParkingSpots(int level, List<ParkingSpot> spots) {
        levelToSpots.putIfAbsent(level, new ArrayList<>());
        levelToSpots.get(level).addAll(spots);
    }

    public ParkingSpot findAvailableSpot(int level, Vehicle vehicle) {
        List<ParkingSpot> spots = levelToSpots.get(level);
        if (spots == null) return null;

        for (ParkingSpot spot : spots) {
            if (spot.canFitVehicle(vehicle)) {
                return spot;
            }
        }
        return null;
    }

    public void allocateSpot(ParkingSpot spot, Vehicle vehicle) {
        spot.parkVehicle(vehicle);
    }

    public void releaseSpot(ParkingSpot spot) {
        spot.unparkVehicle();
    }

    public ParkingSpot getSpot(int level, int spotId) {
        return levelToSpots.get(level).get(spotId);
    }

    public void displayStatus() {
        for (Map.Entry<Integer, List<ParkingSpot>> entry : levelToSpots.entrySet()) {
            System.out.println("Level " + entry.getKey() + ":");
            for (ParkingSpot spot : entry.getValue()) {
                System.out.println("Spot ID: " + spot.getSpotId() + ", Occupied: " + spot.isOccupied());
            }
        }
    }
}

// ParkingStrategy Interface and Implementation
interface ParkingStrategy {
    boolean park(Vehicle vehicle, ParkingSpotManager spotManager);
}

class NearestSpotParkingStrategy implements ParkingStrategy {
    @Override
    public boolean park(Vehicle vehicle, ParkingSpotManager spotManager) {
        for (int level : spotManager.levelToSpots.keySet()) {
            ParkingSpot spot = spotManager.findAvailableSpot(level, vehicle);
            if (spot != null) {
                spotManager.allocateSpot(spot, vehicle);
                return true;
            }
        }
        return false;
    }
}

// PricingStrategy Interface and Implementation
interface PricingStrategy {
    double calculatePrice(long duration, ParkingSpot spot);
}

class FlatRatePricingStrategy implements PricingStrategy {
    private double ratePerHour;

    public FlatRatePricingStrategy(double ratePerHour) {
        this.ratePerHour = ratePerHour;
    }

    @Override
    public double calculatePrice(long duration, ParkingSpot spot) {
        return duration * ratePerHour;
    }
}

class VariableRatePricingStrategy implements PricingStrategy {
    private double bikeRate;
    private double carRate;
    private double truckRate;

    public VariableRatePricingStrategy(double bikeRate, double carRate, double truckRate) {
        this.bikeRate = bikeRate;
        this.carRate = carRate;
        this.truckRate = truckRate;
    }

    @Override
    public double calculatePrice(long duration, ParkingSpot spot) {
        double rate = switch (spot.getSpotType()) {
            case SMALL -> bikeRate;
            case MEDIUM -> carRate;
            case LARGE -> truckRate;
        };
        return duration * rate;
    }
}

// Main Class for Testing
public class p {
    public static void main(String[] args) {
        ParkingSpotManager spotManager = new ParkingSpotManager();
        ParkingStrategy parkingStrategy = new NearestSpotParkingStrategy();
        PricingStrategy pricingStrategy = new VariableRatePricingStrategy(10, 20, 30);

        ParkingLot parkingLot = ParkingLot.getInstance(3, spotManager, parkingStrategy, pricingStrategy);

        List<ParkingSpot> spots = Arrays.asList(
                new ParkingSpot(0, ParkingSpotType.SMALL),
                new ParkingSpot(1, ParkingSpotType.MEDIUM),
                new ParkingSpot(2, ParkingSpotType.LARGE)
        );
        spotManager.addParkingSpots(0, spots);

        Vehicle car = new Car("ABC123");
        if (parkingLot.parkVehicle(car)) {
            System.out.println("Car parked successfully.");
        } else {
            System.out.println("No available spot for the car.");
        }

        parkingLot.displayStatus();
    }
}