/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  17-Jul-2025		Darshan Parmar		MOTADATA-6662: UDP port support
 */

package com.mindarray.agent;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.multipart.MultipartForm;

import java.util.ArrayList;
import java.util.Base64;

import static com.mindarray.ErrorCodes.ERROR_CODE_NO_ITEM_FOUND;
import static com.mindarray.ErrorMessageConstants.PROCESS_TIMED_OUT;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.REMOTE_SESSION_UPDATE;

/**
 * The AgentManager class is responsible for managing agent operations and communication.
 * <p>
 * This verticle handles various agent management tasks including:
 * <ul>
 *   <li>Agent discovery and rediscovery operations</li>
 *   <li>Polling agents for data</li>
 *   <li>Running commands on agents</li>
 *   <li>Sending configuration updates to agents</li>
 *   <li>Managing agent log uploads and downloads</li>
 *   <li>Handling secure communication with agents</li>
 * </ul>
 * <p>
 * The AgentManager acts as a bridge between the Motadata system and the distributed agents,
 * facilitating command execution, data collection, and configuration management. It ensures
 * secure and reliable communication with agents across the network.
 * <p>
 * This class works closely with other components in the agent package, particularly
 * AgentEventProcessor, to provide comprehensive agent management capabilities.
 */
public class AgentManager extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(AgentManager.class, GlobalConstants.MOTADATA_AGENT, "Agent Manager");

    /**
     * Initializes the AgentManager verticle and sets up event bus consumers for agent operations.
     * <p>
     * This method configures multiple event bus consumers to handle different types of agent operations:
     * <ul>
     *   <li>Agent log upload and download</li>
     *   <li>Remote command execution and communication</li>
     *   <li>Agent discovery and rediscovery</li>
     *   <li>Polling agents for data</li>
     *   <li>Running commands on agents</li>
     *   <li>Sending configuration updates to agents</li>
     * </ul>
     * <p>
     * Each consumer is responsible for handling a specific type of agent operation and
     * communicating with the appropriate agent(s) to execute the operation.
     *
     * @param promise Promise to be completed when initialization is done
     */
    @Override
    public void start(Promise<Void> promise)
    {
        try
        {
            var cipherUtil = new CipherUtil();

            var form = MultipartForm.create()
                    .binaryFileUpload("zip", AgentConstants.AGENT_COMPRESSED_LOG, GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + AgentConstants.AGENT_COMPRESSED_LOG, "agent-logs " + GlobalConstants.PATH_SEPARATOR + " zip");

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_AGENT_UPLOAD_LOG, message ->
            {
                var event = message.body();

                try
                {
                    WebClientUtil.getWebClient().post(AgentConfigUtil.getAgentHTTPServerPort(), CommonUtil.getRemoteEventPublisher(), "/upload")
                            .putHeader("content-type", "multipart/form-data")
                            .putHeader(HttpHeaders.COOKIE.toString(), APIConstants.COOKIE)
                            .sendMultipartForm(form, result ->
                            {
                                if (result.succeeded())
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_AGENT, event.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_DOWNLOAD_LOG)
                                            .put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID))
                                            .put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID()).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                            .put(GlobalConstants.FILE_NAME, result.result().bodyAsJsonObject().getString(GlobalConstants.FILE_NAME))
                                            .put(GlobalConstants.RESULT, result.result().bodyAsJsonObject().getString(GlobalConstants.RESULT)));
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    vertx.eventBus().send(EventBusConstants.EVENT_AGENT, event.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_DOWNLOAD_LOG).put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID))
                                            .put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID()).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, result.cause().getMessage()).put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                                }

                                vertx.fileSystem().deleteBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + AgentConstants.AGENT_COMPRESSED_LOG);
                            });
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    vertx.eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_DOWNLOAD_LOG).put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID))
                            .put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID()).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, exception.getCause().getMessage()).put(GlobalConstants.ERROR, exception.getCause().getMessage()));
                }
            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<byte[]>localConsumer(EventBusConstants.EVENT_REMOTE, message ->
            {
                try
                {
                    var buffer = Buffer.buffer(message.body());

                    var topic = buffer.getString(2, buffer.getShortLE(0) + 2);

                    //skipping motadata -> agent manager events
                    if (message.body() != null && topic.contains(EventBusConstants.AGENT_TOPIC) && !topic.contains(EventBusConstants.MOTADATA_MANAGER_TOPIC))
                    {
                        vertx.eventBus().send(REMOTE_SESSION_UPDATE, System.currentTimeMillis());

                        var event = CodecUtil.toJSONObject(cipherUtil.decrypt(buffer.getBytes(2 + buffer.getShortLE(0), buffer.length())));

                        vertx.eventBus().send(REMOTE_SESSION_UPDATE, System.currentTimeMillis());

                        switch (event.getString(EventBusConstants.EVENT_TYPE))
                        {
                            case EventBusConstants.EVENT_AGENT_RESTART ->
                            {
                                var agent = event.getString(AgentConstants.AGENT_TYPE);

                                if (agent != null && ProcessUtil.stop(agent))
                                {
                                    // only start agent if agent.status is yes

                                    if (AgentConfigUtil.getAgentEnableStatus(agent + ".agent.status").equalsIgnoreCase(GlobalConstants.YES))
                                    {
                                        ProcessUtil.start(agent, EventBusConstants.EVENT_AGENT_RESTART);

                                        if (agent.equalsIgnoreCase(AgentConstants.Agent.LOG.getName()))
                                        {
                                            ProcessUtil.start(AgentConstants.Agent.WINDOWS_EVENT_LOG.getName(), EventBusConstants.EVENT_AGENT_RESTART);
                                        }
                                    }
                                }

                                else
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID())
                                            .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_RESTART)
                                            .put(AgentConstants.AGENT_TYPE, agent)
                                            .put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID)).put(GlobalConstants.MESSAGE, String.format("Failed to stop agent %s, reason : agent not found", agent)));
                                }
                            }

                            // If received message of unknown agent then register again
                            case EventBusConstants.EVENT_UNKNOWN -> EventBusConstants.registerAgent(false);

                            case EventBusConstants.EVENT_AGENT_CONFIGURATION_CHANGE ->
                            {
                                var status = false;

                                if (event.getString(Agent.AGENT_CONFIGS) != null)
                                {
                                    status = CommonUtil.dumpConfigs(Agent.AGENT_FILE, new JsonObject(event.getString(Agent.AGENT_CONFIGS)));
                                }

                                // Will send agent configuration update message to server
                                if (status)
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_AGENT, event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID()).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_CONFIGURATION_CHANGE)
                                            .put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID)).put(Agent.AGENT_CONFIGS, event.getString(Agent.AGENT_CONFIGS)));
                                }

                                else
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_AGENT, event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID()).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_CONFIGURATION_CHANGE)
                                            .put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID)).put(GlobalConstants.MESSAGE, "Failed to update agent configuration"));
                                }
                            }

                            case EventBusConstants.EVENT_AGENT_HEARTBEAT ->
                                    vertx.eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ACKNOWLEDGEMENT).put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID())
                                            .put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID)));

                            case EventBusConstants.EVENT_LICENSE_UPDATE ->
                                    LicenseUtil.load(event.getJsonObject(EventBusConstants.EVENT));

                            case EventBusConstants.EVENT_AGENT_DOWNLOAD_LOG ->
                            {
                                LOGGER.info("downloading log files started.....");

                                vertx.<Void>executeBlocking(future ->
                                {

                                    LogUtil.zipLogFiles(event);

                                    future.complete();

                                }, result ->
                                {
                                });
                            }

                            default ->
                            {
                                //do nothing
                            }
                        }

                        if (!OS_AIX)
                        {
                            switch (event.getString(EventBusConstants.EVENT_TYPE))
                            {
                                case EventBusConstants.EVENT_DISCOVERY ->
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ACKNOWLEDGEMENT).put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID())
                                            .put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID)));

                                    EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_QUALIFIED, DateTimeUtil.timestamp()));

                                    discover(event);
                                }

                                case EventBusConstants.EVENT_METRIC_POLL ->
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ACKNOWLEDGEMENT).put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID())
                                            .put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID)));

                                    EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_QUALIFIED, DateTimeUtil.timestamp()));

                                    poll(event);
                                }

                                case EventBusConstants.EVENT_REDISCOVER ->
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ACKNOWLEDGEMENT).put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID())
                                            .put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID)));

                                    EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_QUALIFIED, DateTimeUtil.timestamp()));

                                    rediscover(event);
                                }

                                case EventBusConstants.EVENT_PLUGIN_ENGINE ->
                                {

                                    vertx.eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ACKNOWLEDGEMENT).put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID())
                                            .put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID)));

                                    EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_QUALIFIED, DateTimeUtil.timestamp()));

                                    run(event);
                                }
                                default ->
                                {
                                    // do nothing
                                }
                            }
                        }
                    }
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            promise.complete();
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    /**
     * Executes plugin engine requests on agents.
     * <p>
     * This method handles the execution of various plugin engine requests, such as:
     * <ul>
     *   <li>Metric collection</li>
     *   <li>Command execution</li>
     *   <li>Runbook operations</li>
     *   <li>Custom plugin execution</li>
     * </ul>
     * <p>
     * It supports both streaming and non-streaming modes:
     * <ul>
     *   <li>Streaming mode: Used for long-running operations that produce continuous output</li>
     *   <li>Non-streaming mode: Used for operations that produce a single result</li>
     * </ul>
     * <p>
     * The method spawns appropriate worker processes based on the plugin engine type
     * (Python, PowerShell, etc.) and handles the results, including error processing.
     *
     * @param event The event containing plugin engine request details
     */
    private void run(JsonObject event)
    {
        vertx.<JsonObject>executeBlocking(future ->
        {
            try
            {
                // Ensure the agent ID is available in the event
                if (!event.containsKey(AIOpsObject.OBJECT_AGENT))
                {
                    // Pass discovery ID in object.agent key to continue plugin flow
                    event.put(AIOpsObject.OBJECT_AGENT, event.getLong(ID));
                }

                // Determine the type of plugin engine request (metric, command, runbook, etc.)
                var pluginEngineRequest = PluginEngineConstants.PluginEngineRequest.valueOfName(
                        event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST));

                // Determine the plugin engine to use (Python, PowerShell, etc.)
                var pluginEngine = PluginEngineConstants.PluginEngine.valueOfName(
                        event.getString(PluginEngineConstants.PLUGIN_ENGINE));

                // Check if this is a custom plugin execution
                var customPlugin = PluginEngineConstants.isCustomPlugin(event);

                if (pluginEngineRequest == PluginEngineConstants.PluginEngineRequest.STREAMING)
                {
                    WorkerUtil.spawnWorker(event, 300, true, customPlugin, pluginEngine);
                }

                else
                {
                    if (OS_WINDOWS && pluginEngineRequest == PluginEngineConstants.PluginEngineRequest.RUNBOOK && event.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_TYPE) && event.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE).equalsIgnoreCase(Runbook.RunbookPluginType.TRACE_ROUTE.getName()))
                    {
                        pluginEngine = PluginEngineConstants.PluginEngine.PYTHON;
                    }

                    var result = WorkerUtil.spawnWorker(event, event.containsKey(TIMEOUT) ? event.getInteger(TIMEOUT) : 60, false, customPlugin, pluginEngine);

                    if (result.containsKey(RESULT) && !result.getString(RESULT).isEmpty())
                    {
                        for (var line : result.getString(RESULT).split(VALUE_SEPARATOR_WITH_ESCAPE))
                        {
                            result.clear().mergeIn(new JsonObject(new String(Base64.getDecoder().decode(line.trim().replace("\\n", GlobalConstants.EMPTY_VALUE).replace(VALUE_SEPARATOR, GlobalConstants.EMPTY_VALUE)))));

                            if (customPlugin)
                            {
                                result = event.mergeIn(result);
                            }

                            ErrorMessageConstants.extractError(result);

                            if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                            {
                                result.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS);
                            }

                            else
                            {
                                ErrorMessageConstants.extractErrorCode(result.put(STATUS, STATUS_FAIL), ErrorMessageConstants.PLUGIN_ENGINE_FAILED, null, null, pluginEngineRequest.getName());
                            }

                            future.complete(result);
                        }
                    }

                    else
                    {
                        future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, String.format(ErrorMessageConstants.PLUGIN_ENGINE_FAILED, pluginEngineRequest.getName(), UNKNOWN)));
                    }
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);

                event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR);

                if (exception.getMessage() != null && exception.getMessage().contains(ErrorMessageConstants.STREAM_CLOSED))
                {
                    future.complete(event.put(STATUS, STATUS_TIME_OUT)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT)
                            .put(MESSAGE, String.format(ErrorMessageConstants.PLUGIN_ENGINE_FAILED, event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST), ErrorMessageConstants.PROCESS_TIMED_OUT)));
                }

                else
                {
                    future.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.PLUGIN_ENGINE_FAILED, event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST), exception.getMessage()))
                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
                }
            }
        }, false, result -> send(result.result()));
    }

    /**
     * Performs rediscovery operations on agents to refresh resource information.
     * <p>
     * This method handles different types of rediscovery jobs:
     * <ul>
     *   <li>Network Service rediscovery - Tests port connectivity to network services</li>
     *   <li>Process rediscovery - Discovers running processes on the agent</li>
     *   <li>Windows Service rediscovery - Discovers Windows services on the agent</li>
     *   <li>Other rediscovery types as defined in NMSConstants.RediscoverJob</li>
     * </ul>
     * <p>
     * For network service rediscovery, the method directly tests port connectivity.
     * For other rediscovery types, it spawns appropriate worker processes to perform the discovery.
     * <p>
     * The method also supports auto-provisioning of discovered resources when specified,
     * which automatically updates the agent configuration with the newly discovered resources.
     *
     * @param event The event containing rediscovery job details
     */
    private void rediscover(JsonObject event)
    {
        vertx.<JsonObject>executeBlocking(future ->
        {
            try
            {
                // Ensure the agent ID is available in the event
                if (!event.containsKey(AIOpsObject.OBJECT_AGENT))
                {
                    // Pass monitor ID in object.agent key to continue plugin flow
                    event.put(AIOpsObject.OBJECT_AGENT, event.getLong(ID));
                }

                if (event.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(NMSConstants.RediscoverJob.NETWORK_SERVICE.getName()))
                {
                    EventBusConstants.startEvent(event.getLong(EventBusConstants.EVENT_ID), Thread.currentThread().getName());

                    var objects = event.getJsonArray(NMSConstants.OBJECTS);

                    if (objects != null && !objects.isEmpty())
                    {
                        var probes = new JsonArray();

                        for (var index = 0; index < objects.size(); index++)
                        {
                            var port = objects.getInteger(index);

                            var probe = PortUtil.test(event.getString(AIOpsObject.OBJECT_IP), port, event.getInteger(TIMEOUT, 1))
                                    .put(AIOpsObject.OBJECT_NAME, port);

                            probe.remove(RESULT);

                            probes.add(probe);
                        }

                        event.put(STATUS, STATUS_SUCCEED).put(RESULT, new JsonObject()
                                .put(NMSConstants.OBJECTS, probes));
                    }

                    else
                    {
                        event.put(STATUS, STATUS_ABORT)
                                .put(ERROR_CODE, ERROR_CODE_NO_ITEM_FOUND)
                                .put(MESSAGE, String.format(ErrorMessageConstants.REDISCOVER_FAILED, String.format(ErrorMessageConstants.METRIC_POLLER_NO_OBJECT_FOUND, NMSConstants.Type.NETWORK_SERVICE.getName())));
                    }

                    future.complete(event);
                }

                else
                {
                    var result = WorkerUtil.spawnWorker(event, event.containsKey(TIMEOUT) ? event.getInteger(TIMEOUT) : 60,
                            false, false, PluginEngineConstants.PluginEngine.valueOfName(event.getString(PluginEngineConstants.PLUGIN_ENGINE)));

                    if (result.containsKey(RESULT) && !result.getString(RESULT).isEmpty())
                    {
                        for (var line : result.getString(RESULT).split(VALUE_SEPARATOR_WITH_ESCAPE))
                        {
                            result.clear().mergeIn(new JsonObject(new String(Base64.getDecoder().decode(line.trim().replace("\\n", GlobalConstants.EMPTY_VALUE).replace(VALUE_SEPARATOR, GlobalConstants.EMPTY_VALUE)))));

                            ErrorMessageConstants.extractError(result);

                            if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                            {
                                result.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS);

                                if (result.getJsonArray(NMSConstants.OBJECTS) != null && !result.getJsonArray(NMSConstants.OBJECTS).isEmpty())
                                {
                                    result.put(GlobalConstants.RESULT, new JsonObject()
                                            .put(NMSConstants.OBJECTS, result.getJsonArray(NMSConstants.OBJECTS)));

                                    result.remove(NMSConstants.OBJECTS);

                                    var rediscoverJob = NMSConstants.RediscoverJob.valueOfName(event.getString(NMSConstants.REDISCOVER_JOB));

                                    result.put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED);

                                    if (event.containsKey(NMSConstants.AUTO_PROVISION_STATUS) && event.getString(NMSConstants.AUTO_PROVISION_STATUS).equalsIgnoreCase(YES) && (rediscoverJob == NMSConstants.RediscoverJob.PROCESS || rediscoverJob == NMSConstants.RediscoverJob.WINDOWS_SERVICE))
                                    {
                                        update(result, future);
                                    }

                                    else
                                    {
                                        future.complete(result);
                                    }
                                }

                                else
                                {
                                    future.complete(result);
                                }
                            }

                            else
                            {
                                ErrorMessageConstants.extractErrorCode(result, ErrorMessageConstants.REDISCOVER_FAILED, null, null, null);

                                future.complete(result.put(STATUS, STATUS_FAIL));
                            }
                        }
                    }

                    else
                    {
                        future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, String.format(ErrorMessageConstants.REDISCOVER_FAILED, UNKNOWN)));
                    }
                }
            }

            catch (Exception exception)
            {
                event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR);

                LOGGER.error(exception);

                if (exception.getMessage() != null && exception.getMessage().contains(ErrorMessageConstants.STREAM_CLOSED))
                {
                    event.put(GlobalConstants.STATUS, STATUS_TIME_OUT)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.REDISCOVER_FAILED, ErrorMessageConstants.PROCESS_TIMED_OUT));
                }

                else
                {
                    event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.REDISCOVER_FAILED, exception.getMessage()))
                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()));
                }

                future.complete(event);
            }
        }, false, result -> send(result.result()));
    }

    private void poll(JsonObject event)
    {
        vertx.<JsonObject>executeBlocking(future ->
        {
            try
            {
                if (!event.containsKey(AIOpsObject.OBJECT_AGENT))
                {
                    event.put(AIOpsObject.OBJECT_AGENT, event.getLong(ID)); // pass metric id in object.agent key to continue plugin flow
                }

                if (event.containsKey(AIOpsObject.OBJECT_DISCOVERY_METHOD) && event.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD).equalsIgnoreCase(NMSConstants.DiscoveryMethod.AGENT.name())
                        && !event.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName())) // for eum discovery do not update ip/host
                {
                    event.put(AIOpsObject.OBJECT_IP, "127.0.0.1").put(AIOpsObject.OBJECT_HOST, "localhost");
                }

                if (event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()))
                {
                    var objects = event.getJsonArray(NMSConstants.OBJECTS);

                    EventBusConstants.startEvent(event.getLong(EventBusConstants.EVENT_ID), Thread.currentThread().getName());

                    if (objects != null && !objects.isEmpty())
                    {
                        var millis = System.currentTimeMillis();

                        var probes = new JsonArray();

                        for (var index = 0; index < objects.size(); index++)
                        {
                            var objectName = objects.getJsonObject(index).getString(AIOpsObject.OBJECT_NAME);

                            var probe = PortUtil.test(event.getString(AIOpsObject.OBJECT_IP), CommonUtil.getInteger(objectName.split("\\(")[0].trim()), event.getInteger(TIMEOUT, 1))
                                    .put(NMSConstants.NETWORK_SERVICE, objectName);

                            probes.add(probe.containsKey(RESULT) ? probe.put(NMSConstants.NETWORK_SERVICE_LATENCY, CommonUtil.getLong(probe.remove(RESULT))) : probe);
                        }

                        event.put(EventBusConstants.EVENT_LATENCY, System.currentTimeMillis() - millis).put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS)
                                .put(RESULT, new JsonObject().put(NMSConstants.NETWORK_SERVICE, probes));

                    }

                    else
                    {
                        event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                .put(ERROR_CODE, ERROR_CODE_NO_ITEM_FOUND)
                                .put(MESSAGE, String.format(ErrorMessageConstants.METRIC_POLL_FAILED, "Port not found"));

                    }

                    future.complete(event);
                }

                else if (!NMSConstants.portCheckRequire(NMSConstants.Type.valueOfName(event.getString(Metric.METRIC_TYPE)))
                        || (!event.containsKey(PORT) || (event.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.PORT.getName()) && event.containsKey(NMSConstants.PORT_TYPE) && event.getString(NMSConstants.PORT_TYPE).equalsIgnoreCase(NMSConstants.UDP))
                        || PortUtil.test(event.getString(AIOpsObject.OBJECT_IP), event.getInteger(GlobalConstants.PORT), event.getInteger(TIMEOUT, 1)).getString(STATUS).equalsIgnoreCase(STATUS_UP)))
                {
                    var millis = System.currentTimeMillis();

                    var result = WorkerUtil.spawnWorker(event, event.containsKey(TIMEOUT) ? event.getInteger(TIMEOUT) : 60, false, PluginEngineConstants.isCustomPlugin(event),
                            PluginEngineConstants.PluginEngine.valueOfName(event.getString(PluginEngineConstants.PLUGIN_ENGINE))); // pseudo code

                    millis = System.currentTimeMillis() - millis;

                    if (result.containsKey(RESULT) && !result.getString(RESULT).isEmpty())
                    {
                        for (var line : result.getString(RESULT).split(VALUE_SEPARATOR_WITH_ESCAPE))
                        {
                            result.clear().mergeIn(new JsonObject(new String(Base64.getDecoder().decode(line.trim().replace("\\n", GlobalConstants.EMPTY_VALUE).replace(VALUE_SEPARATOR, GlobalConstants.EMPTY_VALUE)))));

                            if (PluginEngineConstants.isCustomPlugin(event))
                            {
                                result = event.mergeIn(result);
                            }

                            result.put(EventBusConstants.EVENT_LATENCY, millis);

                            ErrorMessageConstants.extractError(result);

                            if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                            {
                                result.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS);
                            }
                            else
                            {
                                ErrorMessageConstants.extractErrorCode(result.put(STATUS, STATUS_FAIL), ErrorMessageConstants.METRIC_POLL_FAILED, null, null, null);
                            }

                            future.complete(result);
                        }
                    }

                    else
                    {
                        future.complete(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE,
                                String.format(ErrorMessageConstants.METRIC_POLL_FAILED, UNKNOWN)));
                    }
                }

                else
                {
                    future.complete(event.put(STATUS, STATUS_FAIL)
                            .put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PORT).put(MESSAGE, String.format(ErrorMessageConstants.PORT_CONNECTION_FAILED, event.getInteger(PORT)))))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PORT)
                            .put(MESSAGE, String.format(ErrorMessageConstants.METRIC_POLL_FAILED, String.format(ErrorMessageConstants.PORT_CONNECTION_FAILED, event.getInteger(PORT)))));
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);

                event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR);

                if (exception.getMessage() != null && exception.getMessage().contains(ErrorMessageConstants.STREAM_CLOSED))
                {
                    future.complete(event.put(STATUS, STATUS_TIME_OUT)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT)
                            .put(MESSAGE, String.format(ErrorMessageConstants.METRIC_POLL_FAILED, ErrorMessageConstants.PROCESS_TIMED_OUT)));
                }

                else
                {
                    future.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.METRIC_POLL_FAILED, exception.getMessage()))
                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
                }
            }
        }, false, result -> send(result.result()));
    }

    private void discover(JsonObject event)
    {


        vertx.<JsonObject>executeBlocking(future ->
        {
            try
            {
                if (!event.containsKey(AIOpsObject.OBJECT_AGENT))
                {
                    event.put(AIOpsObject.OBJECT_AGENT, event.getLong(ID)); // pass discovery id in object.agent key to continue plugin flow
                }

                if (event.containsKey(Discovery.DISCOVERY_METHOD) && event.getString(Discovery.DISCOVERY_METHOD).equalsIgnoreCase(NMSConstants.DiscoveryMethod.AGENT.name())
                        && !event.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName())) // for eum discovery do not update ip/host
                {
                    event.put(AIOpsObject.OBJECT_IP, "127.0.0.1").put(AIOpsObject.OBJECT_HOST, "localhost");
                }

                if (NMSConstants.Type.valueOfName(event.getString(AIOpsObject.OBJECT_TYPE)) == NMSConstants.Type.PING)
                {
                    var status = PingUtil.ping(event.getString(AIOpsObject.OBJECT_IP), 1, event.getInteger(NMSConstants.PING_CHECK_RETRIES, 3), event.getLong(EventBusConstants.EVENT_ID)).getString(STATUS);

                    if (status.equalsIgnoreCase(STATUS_UP))
                    {
                        event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS)
                                .put(STATUS, STATUS_SUCCEED)
                                .put(NMSConstants.OBJECTS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(AIOpsObject.OBJECT_TARGET, event.getString(AIOpsObject.OBJECT_TARGET))))
                                .put(MESSAGE, InfoMessageConstants.DISCOVERY_OBJECT_DISCOVERED);
                    }

                    else
                    {
                        event.put(STATUS, status.equalsIgnoreCase(STATUS_TIME_OUT) ? status : STATUS_FAIL).put(Discovery.DISCOVERY_PROGRESS, 100.0)
                                .put(ERROR_CODE, status.equalsIgnoreCase(STATUS_TIME_OUT) ? ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT : ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                .put(MESSAGE, String.format(ErrorMessageConstants.DISCOVERY_FAILED, status.equalsIgnoreCase(STATUS_TIME_OUT) ? ErrorMessageConstants.PROCESS_TIMED_OUT : String.format(ErrorMessageConstants.PING_FAILED, event.getString(AIOpsObject.OBJECT_TARGET))));
                    }

                    future.complete(event);
                }

                else if (!NMSConstants.portCheckRequire(NMSConstants.Type.valueOfName(event.getString(AIOpsObject.OBJECT_TYPE)))
                        || (!event.containsKey(PORT) || (event.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.PORT.getName()) && event.containsKey(NMSConstants.PORT_TYPE) && event.getString(NMSConstants.PORT_TYPE).equalsIgnoreCase(NMSConstants.UDP))
                        || PortUtil.test(event.getString(AIOpsObject.OBJECT_IP), event.getInteger(GlobalConstants.PORT), event.getInteger(TIMEOUT, 1)).getString(STATUS).equalsIgnoreCase(STATUS_UP)))
                {
                    var result = WorkerUtil.spawnWorker(event, event.containsKey(TIMEOUT) ? event.getInteger(TIMEOUT) : 60,
                            false, false, PluginEngineConstants.PluginEngine.valueOfName(event.getString(PluginEngineConstants.PLUGIN_ENGINE)));

                    if (result.containsKey(RESULT) && !result.getString(RESULT).isEmpty())
                    {
                        for (var line : result.getString(RESULT).split(VALUE_SEPARATOR_WITH_ESCAPE))
                        {
                            result.clear().mergeIn(new JsonObject(new String(Base64.getDecoder().decode(line.trim().replace("\\n", GlobalConstants.EMPTY_VALUE).replace(VALUE_SEPARATOR, GlobalConstants.EMPTY_VALUE)))));

                            result.put(Discovery.DISCOVERY_PROGRESS, 100.00);

                            if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                            {
                                result.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS)
                                        .put(MESSAGE, CommonUtil.isNotNullOrEmpty(result.getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)) ? String.format(InfoMessageConstants.DISCOVERY_CREDENTIAL_PROFILE_SUCCEEDED, result.getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)) : InfoMessageConstants.DISCOVERY_OBJECT_DISCOVERED);
                            }

                            else
                            {
                                ErrorMessageConstants.extractErrorCode(result, ErrorMessageConstants.DISCOVERY_FAILED, null, null, null);
                            }

                            future.complete(result);
                        }
                    }

                    else
                    {
                        future.complete(event.put(STATUS, STATUS_FAIL).put(Discovery.DISCOVERY_PROGRESS, 100.0)
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                .put(MESSAGE, String.format(ErrorMessageConstants.DISCOVERY_FAILED, UNKNOWN)));
                    }
                }

                else
                {
                    future.complete(event.put(STATUS, STATUS_FAIL).put(Discovery.DISCOVERY_PROGRESS, 100.0)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PORT)
                            .put(MESSAGE, String.format(ErrorMessageConstants.DISCOVERY_FAILED, ErrorMessageConstants.DISCOVERY_PORT_NOT_REACHABLE)));
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);

                event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR);

                if (exception.getMessage() != null && (exception.getMessage().contains(ErrorMessageConstants.STREAM_CLOSED) || exception.getMessage().contains(PROCESS_TIMED_OUT)))
                {
                    future.complete(event.put(GlobalConstants.STATUS, STATUS_TIME_OUT)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.DISCOVERY_FAILED, ErrorMessageConstants.PROCESS_TIMED_OUT)));
                }

                else
                {
                    future.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.DISCOVERY_FAILED, exception.getMessage()))
                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
                }
            }
        }, false, result -> send(result.result()));
    }

    private void send(JsonObject event)
    {
        if (CommonUtil.parsingRequired(event))
        {
            try
            {
                var eventId = CommonUtil.newEventId();

                EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_CHILD_EVENT_FORKED, eventId, DateTimeUtil.timestamp()));

                var output = WorkerUtil.spawnWorker(event, eventId);

                if (CommonUtil.isNotNullOrEmpty(output))
                {
                    var result = new JsonObject(new String(Base64.getDecoder().decode(output)));

                    ErrorMessageConstants.extractError(result);

                    event.mergeIn(result, true);

                    if (event.getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_METRIC_POLL) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                    {
                        NMSConstants.validateJSONObject(event);
                    }

                    if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                    {
                        event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS);
                    }
                    else
                    {
                        ErrorMessageConstants.extractErrorCode(event.put(STATUS, STATUS_FAIL), ErrorMessageConstants.PARSING_SCRIPT_FAILED, null, null, null);
                    }
                }
                else
                {
                    event.put(STATUS, STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.PARSING_SCRIPT_FAILED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PARSING_SCRIPT);
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }

        EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_RESPONSE_SENT, DateTimeUtil.timestamp()));

        vertx.eventBus().send(EventBusConstants.EVENT_AGENT, event);
    }

    private void update(JsonObject event, Promise<JsonObject> future)
    {
        var objects = event.getJsonObject(GlobalConstants.RESULT).getJsonArray(NMSConstants.OBJECTS);

        if (!objects.isEmpty())
        {
            var configs = new JsonObject(vertx.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "agent.json"));

            configs.getJsonObject(Agent.AGENT_METRIC).put(event.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(NMSConstants.RediscoverJob.PROCESS.getName()) ? NMSConstants.PROCESSES : NMSConstants.SERVICES, objects);

            CommonUtil.dumpConfigs(Agent.AGENT_FILE, configs);

            event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(Agent.AGENT_CONFIGS, configs.encode());
        }

        else
        {
            LOGGER.info(String.format("failed to update %s , reason : discovered objects are null or empty", event.getString(NMSConstants.REDISCOVER_JOB)));
        }

        // send motadata server to update in config
        future.complete(event);
    }
}
