/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.db;

import com.mindarray.util.MotadataConfigUtil;
import io.vertx.codegen.annotations.Fluent;
import io.vertx.codegen.annotations.GenIgnore;
import io.vertx.codegen.annotations.ProxyGen;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.sqlclient.Pool;

@ProxyGen
public interface ComplianceDBService
{

    @GenIgnore
    static void create(Vertx vertx, Pool db, Handler<AsyncResult
            <ComplianceDBService>> handler)
    {
        new ComplianceDBServiceImpl(vertx, db, handler);
    }

    @GenIgnore
    static ComplianceDBService createProxy(Vertx vertx, String address)
    {
        return new ComplianceDBServiceVertxEBProxy(vertx, address, new DeliveryOptions().setSendTimeout(MotadataConfigUtil.getDBServiceTimeoutMillis()));
    }

    Future<Long> save(String table, JsonObject record, String user, String remoteIP);

    Future<Long> execute(String table, String record, String user, String remoteIP);

    Future<Long> saveAll(String table, JsonArray records, String user, String remoteIP);

    Future<JsonObject> get(String table, JsonObject query);

    Future<JsonArray> delete(String table, JsonObject query, String user, String remoteIP);
}
